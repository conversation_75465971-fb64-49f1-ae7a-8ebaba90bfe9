<?xml version="1.0" encoding="UTF-8"?>
<svg width="516px" height="309px" viewBox="0 0 516 309" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>单孔托盘/黑色备份</title>
    <defs>
        <linearGradient x1="32.7167619%" y1="44.1407985%" x2="54.9530303%" y2="58.551186%" id="linearGradient-1">
            <stop stop-color="#747779" offset="0%"></stop>
            <stop stop-color="#65686B" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-2" points="224.323631 10.0441957 3.37956441 153.711953 281.373048 232.192957 503.379564 87.2978285"></polygon>
        <filter x="-0.2%" y="-0.5%" width="100.4%" height="100.9%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.725286069   0 0 0 0 0.725286069   0 0 0 0 0.725286069  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M3.37956441,153.711953 L3.37956441,200.210513 L41.984653,210.59346 C43.1658881,210.911156 44.3810122,210.21112 44.6987087,209.029885 C44.74916,208.842301 44.7747145,208.648895 44.7747145,208.454645 L44.7747145,171.680052 L44.7747145,171.680052 L240.068162,226.769948 L240.068162,245.257833 L240.068162,264.250699 C240.068162,265.231909 240.71376,266.096165 241.654658,266.374526 L281.099753,278.044196 L281.099753,278.044196 L281.099753,232.192957 L3.37956441,153.711953 Z" id="path-4"></path>
        <filter x="-0.5%" y="-1.2%" width="101.1%" height="102.4%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.53118808   0 0 0 0 0.53118808   0 0 0 0 0.53118808  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M503.379564,87.2978285 L503.379564,132.048369 L281.099753,278.044196 L281.099753,232.192957 L503.379564,87.2978285 Z M365.724767,185.186129 L308.694286,222.926394 L308.694286,251.15627 L365.724767,213.771081 L365.724767,185.186129 Z M477.573236,112.124593 L420.542754,149.864858 L420.542754,179.201726 L477.573236,141.816538 L477.573236,112.124593 Z" id="path-6"></path>
        <filter x="-0.4%" y="-0.5%" width="100.9%" height="101.0%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.620595739   0 0 0 0 0.620595739   0 0 0 0 0.620595739  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="2022.12.20_容器" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="容器图形（带标尺）" transform="translate(-855.000000, -188.000000)">
            <g id="单孔托盘/黑色备份" transform="translate(858.620436, 193.955804)">
                <path d="M44.7747145,209.129869 C71.8754844,191.457378 85.4258693,182.621133 85.4258693,182.621133 C85.4258693,182.621133 71.8754844,178.974106 44.7747145,171.680052 L44.7747145,209.129869 Z" id="路径-11" fill="#454647"></path>
                <g id="路径-7">
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                </g>
                <g id="路径-8">
                    <use fill="#636364" fill-rule="evenodd" xlink:href="#path-4"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                </g>
                <g id="形状结合">
                    <use fill="#464648" fill-rule="evenodd" xlink:href="#path-6"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                </g>
                <path d="M336.188012,213.771081 C355.879182,200.820262 365.724767,194.344853 365.724767,194.344853 C365.724767,194.344853 355.879182,191.299788 336.188012,185.209658 L336.188012,213.771081 Z" id="路径-11备份-3" fill="#2C2D2F" transform="translate(350.956390, 199.490370) scale(-1, -1) translate(-350.956390, -199.490370) "></path>
                <path d="M445.638895,141.816538 C466.928455,127.796511 477.573236,120.786497 477.573236,120.786497 C477.573236,120.786497 466.928455,117.899196 445.638895,112.124593 L445.638895,141.816538 Z" id="路径-11备份-4" fill="#2C2D2F" transform="translate(461.606065, 126.970566) scale(-1, -1) translate(-461.606065, -126.970566) "></path>
                <g id="单孔托盘/黑色/标注">
                    <path id="路径-12备份-2" d="M230.439702,-5.01721811 L231.884262,-4.61317884 L230.739,-0.52 L508.815,77.256 L509.959837,73.1637828 L511.404396,73.567822 L508.710801,83.198219 L507.266242,82.7941797 L508.41,78.701 L230.335,0.924 L229.190667,5.01721811 L227.746107,4.61317884 L230.439702,-5.01721811 Z" fill="#00D4D0" fill-rule="nonzero"></path>
                    <path id="路径-12备份-3" d="M214.269906,-4.59476236 L219.744474,3.77357717 L218.489223,4.59476236 L216.162,1.038 L1.038,141.772 L3.36490941,145.329093 L2.10965848,146.150278 L-3.36490941,137.781938 L-2.10965848,136.960753 L0.217,140.517 L215.341,-0.217 L213.014655,-3.77357717 L214.269906,-4.59476236 Z" fill="#00D4D0" fill-rule="nonzero"></path>
                    <text id="宽备份-3" transform="translate(359.601898, 19.904571) rotate(15.000000) translate(-359.601898, -19.904571) " font-family="PingFangTC-Medium, PingFang TC" font-size="14" font-weight="400" fill="#00DDD9">
                        <tspan x="352.601898" y="24.9045706">width</tspan>
                    </text>
                    <text id="长" transform="translate(96.555906, 58.610409) rotate(-33.000000) translate(-96.555906, -58.610409) " font-family="PingFangTC-Medium, PingFang TC" font-size="14" font-weight="400" fill="#00DDD9">
                        <tspan x="89.5559064" y="63.6104092">length</tspan>
                    </text>
                    <text id="孔洞宽备份-2" transform="translate(129.479911, 256.808640) rotate(15.000000) translate(-129.479911, -256.808640) " font-family="PingFangTC-Medium, PingFang TC" font-size="14" font-weight="400" fill="#00DDD9">
                        <tspan x="108.479911" y="261.80864">Hole width</tspan>
                    </text>
                    <text id="宽备份-4" transform="translate(16.956949, 222.185173) rotate(15.000000) translate(-16.956949, -222.185173) " font-family="PingFangTC-Medium, PingFang TC" font-size="14" font-weight="400" fill="#00DDD9">
                        <tspan x="9.95694945" y="227.185173">width</tspan>
                    </text>
                    <text id="边缘立柱备份-2" transform="translate(24.255166, 180.515747) rotate(15.000000) translate(-24.255166, -180.515747) " font-family="PingFangTC-Medium, PingFang TC" font-size="10" font-weight="400" fill="#00DDD9">
                        <tspan x="4.25516614" y="184.515747">Edge pillar</tspan>
                    </text>
                    <text id="边缘立柱备份-3" transform="translate(261.255166, 248.515747) rotate(15.000000) translate(-261.255166, -248.515747) " font-family="PingFangTC-Medium, PingFang TC" font-size="10" font-weight="400" fill="#00DDD9">
                        <tspan x="241.255166" y="252.515747">Edge pillar</tspan>
                    </text>
                    <text id="宽备份-5" transform="translate(250.956949, 291.185173) rotate(15.000000) translate(-250.956949, -291.185173) " font-family="PingFangTC-Medium, PingFang TC" font-size="14" font-weight="400" fill="#00DDD9">
                        <tspan x="243.956949" y="296.185173">width</tspan>
                    </text>
                    <path d="M229.815184,272.544196 L46.8795644,220.544196 M46.8795644,217.544196 L46.8795644,224.544196 M230.815184,269.544196 L230.815184,276.544196" id="形状结合备份-4" stroke="#00D4D0" stroke-width="1.5" stroke-linecap="square"></path>
                    <path d="M38.8795644,217.544196 L2.87956441,207.544196 M2.87956441,204.544196 L2.87956441,211.544196 M39.8795644,214.544196 L39.8795644,221.544196" id="形状结合备份-5" stroke="#00D4D0" stroke-width="1.5" stroke-linecap="square"></path>
                    <path d="M272.879564,284.544196 L236.879564,274.544196 M236.879564,271.544196 L236.879564,278.544196 M273.879564,281.544196 L273.879564,288.544196" id="形状结合备份-6" stroke="#00D4D0" stroke-width="1.5" stroke-linecap="square"></path>
                    <text id="孔洞高备份-2" transform="translate(162.137063, 223.375193) rotate(-345.000000) translate(-162.137063, -223.375193) " font-family="PingFangTC-Medium, PingFang TC" font-size="10" font-weight="400" fill="#00DDD9">
                        <tspan x="147.137063" y="227.375193">Hole height</tspan>
                    </text>
                    <path d="M140.879564,199.544196 L140.879564,236.544196 M136.176843,197.881084 L146.378111,200.922359 M135.176843,234.881084 L145.378111,237.922359" id="形状结合" stroke="#00D4D0" stroke-width="1.5" stroke-linecap="square"></path>
                </g>
            </g>
        </g>
    </g>
</svg>