/**
 * 一些属性的设置
 */
import { defineStore } from "pinia";
import { getSelectNodeType } from "@packages/hook/useRightFrom";
import { MapNodeDto, MapSegmentDto, MapAreaDto } from "@packages/type/editNode";
import { GetFunctionalLabelsResult } from "@packages/api/map/type/getFunctionalLabels";
import { useEditMap } from "@packages/hook/useEdit";
import {
  findAdjacentByCellCode,
  getAllRobotTypes,
  getShelfModelList,
  robotInstruction,
  findContainerModelByPage,
  getSingleLaneTypes,
  getStopModeTypes
} from "@packages/api/map";
import { FindAdjacentByCellCodeParams } from "@packages/api/map/type/findAdjacentByCellCode";
import { ElMessage } from "element-plus";
import { getComponentStore } from "./piniaSync";

const editRef = useEditMap();

// 这里是全局可用的属性控制
interface AttrStoreType {
  selectNodes: (MapNodeDto | MapSegmentDto | MapAreaDto)[];
  selectIndex: number;
  layerName: string | undefined;
  storedCellCodes: { id: number; cellCode: string, nodeId: string }[];

  // 全局需要用到的一些数据
  holderTypeDict: any[];
  functionalLabels: GetFunctionalLabelsResult[];
  findDistinctSizeTypeDict: { label: string; value: string }[];
  findAdjacentByCellCodeDictMap: { [k: string]: { label: string; value: string }[] };

  // robot类型
  robotTypeList: any[] | null;
  // 货架模型
  shelfModelList: any[] | null;
  // 地面支架模型
  holderModelList: any[] | null;
  // 支持的指令类型
  robotInstructionDict: any[] | null;
  // 支持的托盘架模型列表
  containerModelDict: any[] | null;
  // 单行道类型
  singleLineList: any[] | null;
  stopModeTypeList: any[] | null;
  // 是否是全局点位编辑状态
  isGlobalCell: boolean;

  // 全局点位编辑数据
  moveOperationDtos: any[];
  drawAreaType: string;
  isAreaAdd: boolean;
  // 一些特殊操作
  curOperationBySpecial: "none" | "drawArea" | "lineType" | "formatBrush";
  curSelectType: "CELL" | "DEVICE" | "LINE" | "AREA";
  temporaryData: { [k: string]: any } | null;
  // 是否正在批量添加点位
  isAddBatchCellPattern: boolean;
  // 批量添加时的那个线的数据
  addBatchByLineData: MapSegmentDto | null;
  // 是否处于多选模式
  isMultipleSelectMode: boolean;
  //是否处于功能选择模式
  isFnChooseMode: boolean;
  // 是否处于单选模式(不是普通的状态, 而是某些需要单点选点的场景)
  isSingleSelectMode: boolean;
  multipleSelTemporaryData: {
    selectNodes: (MapNodeDto | MapSegmentDto | MapAreaDto)[];
    selectIndex: number;
    layerName: string | undefined;
  } | null;
  isBatchModifyCellFun: boolean;
  // 全局loading
  globalLoading: boolean;
}

export const useAttrStore = defineStore({
  id: "attrData",
  state: (): AttrStoreType => {
    return {
      selectNodes: [],
      selectIndex: 0,
      layerName: undefined,
      storedCellCodes: [],
      // ⭐ 上面3个不要动, 跟DICT没关系
      holderTypeDict: [],
      functionalLabels: [],
      findDistinctSizeTypeDict: [],
      findAdjacentByCellCodeDictMap: {},
      // 支持的指令类型
      robotInstructionDict: null,
      // 单行道类型
      singleLineList: null,
      stopModeTypeList: null,
      // robot类型
      robotTypeList: null,
      // 货架模型
      shelfModelList: null,
      // 地面支架模型
      holderModelList: null,
      // 托盘架模型列表
      containerModelDict: null,
      // 全局点位编辑
      isGlobalCell: false,
      // 全局点位编辑数据
      moveOperationDtos: [],
      // 绘制的区域类型
      drawAreaType: "none",
      //是否处于区域添加态
      isAreaAdd: false,
      // 这里仅表示一些常见的
      curOperationBySpecial: "none",
      // 当前操作类型
      curSelectType: "CELL",
      // 临时缓存数据, 只支持缓存1个, 当创建区域时, 使用的便是这个变量去做预存储
      temporaryData: null,
      // 是否正在批量添加点位
      isAddBatchCellPattern: false,
      // 是否处于多选模式
      isMultipleSelectMode: false,
      // 是否处于单选模式(不是普通的状态, 而是某些需要单点选点的场景)
      isSingleSelectMode: false,
      //是否处于功能选择模式
      isFnChooseMode: false,
      // 批量添加时的那个线的数据
      addBatchByLineData: null,
      /**
       * 这里新增一个特殊处理, 当处于多选模式时
       * 点位选中态需要全部被清空, 此时可能还处于某个点的选中
       * 此时需要清除点位选中的同时保留当前点位的数据, 后续点位选择结束后重新选择当前点位
       */
      multipleSelTemporaryData: null,
      // 是否是批量编辑点击功能模式
      isBatchModifyCellFun: false,
      // 全局loading
      globalLoading: false,
    };
  },
  getters: {
    curNodeDataByIndex(): MapNodeDto | MapSegmentDto | MapAreaDto | undefined {
      if (this.isMultipleSelectMode || this.isSingleSelectMode) {
        const { selectNodes, selectIndex } = <any>this.multipleSelTemporaryData;
        return selectNodes[selectIndex];
      }
      return this.selectNodes[this.selectIndex];
    },
    curNodeTypeByIndex() {
      return getSelectNodeType(this.curNodeDataByIndex || {});
    },
    // 当前选择的点位所使用的cellCode, ⭐ 注意这里的cellCode表示保存前的cellCode, 并非实时
    curNodeCellCodeByIndex() {
      const { curNodeDataByIndex } = this;
      if (!curNodeDataByIndex) {
        return;
      }
      const storedCellCodes: any = this.storedCellCodes;
      return (<{ id: number; cellCode: string }[]>storedCellCodes).find(item => {
        return item.id === (<any>curNodeDataByIndex).id;
      })?.cellCode;
    },
    robotTypeDict() {
      const robotTypeList: any = this.robotTypeList || [];
      return robotTypeList.map((item: any) => {
        return {
          label: item.product,
          value: item.product,
        };
      });
    },
    //value值是机器人类型
    robotTypeDict2() {
      const robotTypeList: any = this.robotTypeList || [];
      return robotTypeList.map((item: any) => {
        return {
          label: item.product,
          value: item.product,
        };
      });
    },
    shelfModelDict() {
      const shelfModelList: any[] = this.shelfModelList || [];
      return shelfModelList.map(item => {
        return {
          // label: item.name,
          // value: item.value,
          label: item.modelType,
          value: item.modelType,
        };
      });
    },
    /**
     * 全局锁定状态的判断
     * 全局锁定状态下不能使用左侧/顶部面板/检索/编辑背景图/菜单
     * @returns
     */
    isGlobalDisabled() {
      if (
        [this.isGlobalCell, this.isFnChooseMode, this.isAddBatchCellPattern, this.isMultipleSelectMode, this.isSingleSelectMode].includes(true)
      ) {
        return true;
      }

      // 批量编辑点位属性/复制等操作会进入锁定状态
      const menuStore = getComponentStore("menuStore")();
      if (menuStore && (menuStore.isBatchModifyCellFun || menuStore.isCopyMode)) {
        return true;
      }

      return false;
    },
  },
  actions: {
    /**
     * 设置选中
     * 这里应该只有map:select中使用
     * 只是数据层面的选中, 调用不能选中界面的元素
     * @param option
     */
    setCurSelect(option: (MapNodeDto | MapSegmentDto | MapAreaDto)[]) {
      this.selectNodes = [...option];
      this.selectIndex = 0;
    },

    setCurIndexNodeData(option: MapNodeDto | MapSegmentDto | MapAreaDto) {
      const selectNodes = [...this.selectNodes];
      selectNodes[this.selectIndex] = option;
      this.selectNodes = selectNodes;
    },

    setLayerName(layerName: string) {
      this.layerName = layerName;
    },

    /**
     * 添加选中节点
     */
    appendSelect(option: MapNodeDto) {
      this.selectNodes.push(option);
      this.selectIndex = this.selectNodes.length - 1;
    },

    /**
     * 清空选中节点
     */
    clearSelect() {
      this.selectNodes = [];
      this.layerName = undefined;
    },

    setHolderTypeDict(data: any[]) {
      this.holderTypeDict = data;
    },

    setFunctionalLabels(data: GetFunctionalLabelsResult[]) {
      this.functionalLabels = data;
    },

    setFindDistinctSizeType(data: string[]) {
      this.findDistinctSizeTypeDict = data.map(item => ({ label: (item || ''), value: (item || '') }));
    },

    /**
     * 请求避让点集合(读缓存)
     * @param params
     * @returns
     */
    async getFindAdjacentByCellCodeDictByOnly(params: FindAdjacentByCellCodeParams) {
      let curData = this.findAdjacentByCellCodeDictMap[params.cellCode];
      if (curData) {
        return curData;
      }
      return await this.getFindAdjacentByCellCodeDict(params);
    },

    /**
     * 请求避让点集合(不读缓存)
     * @param params
     * @returns
     */
    async getFindAdjacentByCellCodeDict(params: FindAdjacentByCellCodeParams) {
      if (!params.cellCode) {
        // 当节点编码为空的时候配置功能的时候提示的报错信息
        // ElMessage.error("请先保存当前点位再配置该功能！");
        return [];
      }
      const { code, data } = await findAdjacentByCellCode(params);
      if (code === 0) {
        const curData = data.map(item => ({ label: item.cellCode, value: item.cellCode, nodeId: item.nodeId }));
        this.findAdjacentByCellCodeDictMap[params.cellCode] = curData;
        return curData;
      }
      return data;
    },

    /**
     * 请求所有机器人的数据
     * @returns
     */
    async getAllRobotTypesByOnly() {
      if (this.robotTypeList) {
        return this.robotTypeList;
      }
      return await this.getAllRobotTypes();
    },

    async getAllRobotTypes() {
      const { code, data } = await getAllRobotTypes();
      if (code === 0) {
        this.robotTypeList = data.map((item: any) => {
          return {
            id: item?.id,
            ...item,
          }
        });
      }
      return data;
    },
    //获取单行道类别
    async getSingleLaneTypeList() {
      if(this.singleLineList){
        return this.singleLineList
      }else{
        const {code,data} = await getSingleLaneTypes()
        if (code === 0) {
          this.singleLineList = data.map((item: {}) => {
            // @ts-ignore
            const {dictKey,dictValue} = item
            return {value:Number(dictKey),label:dictValue}
          });
        }
      }
    },

    async getStopModeTypeList() {
      if(this.stopModeTypeList){
        return this.stopModeTypeList
      }else{
        const {code,data} = await getStopModeTypes()
        if (code === 0) {
          this.stopModeTypeList = data || []
            .sort((itemA: any, itemB: any)=> itemA.sortOrder - itemB.sortOrder)
            .map((item: {}) => {
            // @ts-ignore
            const { dictKey,dictValue, } = item
            return { value: dictKey, label: dictValue }
          });
        }
      }
    },

    /**
     * 获取可用的货架模型
     * @returns
     */
    async getShelfModelListByOnly() {
      if (this.shelfModelList) {
        return this.shelfModelList;
      }
      return await this.getShelfModelList();
    },

    async getHolderModelListByOnly() {
      if (this.holderModelList) {
        return this.holderModelList;
      }
      return await this.getHolderModelList();
    },

    async getShelfModelList() {
      const { code, data } = await getShelfModelList({
        type: "shelf",
      });
      if (code === 0) {
        this.shelfModelList = data;
      }
      return data;
    },

    async getHolderModelList() {
      const { code, data } = await getShelfModelList({
        type: "holder",
      });
      if (code === 0) {
        this.holderModelList = data;
      }
      return data;
    },

    /**
     * 获取可用的指令
     * @returns
     */
    async getRobotInstructionByOlny() {
      if (this.robotInstructionDict) {
        return this.robotInstructionDict;
      }
      return await this.getRobotInstruction();
    },
    /**
     * 解析
     * @returns
     */
    async getRobotInstruction() {
      const { code, data } = await robotInstruction();
      if (code === 0) {
        this.robotInstructionDict = data.map(item => {
          return {
            label: item,
            value: item,
          };
        });
      }
      return data;
    },
    /**
     * 获取可用托盘架模型
     * @returns
     */
    async getContainerModelByOlny() {
      if (this.containerModelDict) {
        return this.containerModelDict;
      }
      return await this.getContainerModel();
    },

    async getContainerModel() {
      const { code, data } = await findContainerModelByPage({
        currentPage: 1,
        pageSize: 99999,
        modelCategory: 'PALLET_RACK',
      });

      if (code === 0) {
        this.containerModelDict = data.recordList.map(item => {
          return {
            label: item.modelName || "",
            value: item.id,
            layer: item.layout,
            detail: item.extendJson?.detail || []
          };
        });
      }
      return data;
    },

    getAreaInfoByType() {
      const info = editRef.value?.getAreaInfoByType('SORTING_AREA')
      const ops = info!.map(item => {
        const {properties} = item
        return {
          label: properties.areaId.toString(),
          value: properties.areaId,
        };
      })
      return ops
    },
    /**
     * 获取所有的单元格信息,
     */
    getCellData() {
      const layerData = editRef.value?.getLayerData('CELL')
      let list = layerData?.filter(item => item.nodeId) || []
      list = list?.map(item => {
        const {cellCode,nodeId} = item
        return {
          label: cellCode,
          value: cellCode,
          nodeId
        }
      })
      return list
    },
    /**
     * 设置已经存储的点位id,
     */
    setStoredCellCodes(mapFloorDtoList: MapNodeDto[]) {
      this.storedCellCodes = mapFloorDtoList.map(item => {
        return {
          id: item.id,
          cellCode: item.cellCode,
          nodeId: String(item.nodeId),
        };
      });
    },

    /**
     * 设置是否进入全局点位编辑模式
     * @param visable
     */
    setGlobalCell(visable: boolean) {
      this.isGlobalCell = visable;
    },

    /**
     * 设置全局点位编辑数据
     * @param moveOperationDtos
     */
    setMoveOperationDtos(moveOperationDtos: any[]) {
      this.moveOperationDtos = moveOperationDtos;
    },

    /**
     * 设置是在绘制的区域类型, none是没有绘制区域
     */
    setDrawAreaType(type: string) {
      this.drawAreaType = type;
    },
    /**
     * 设置是在绘制的区域类型, none是没有绘制区域
     */
    setAreaAddStatus(isAdd: boolean) {
      this.isAreaAdd = isAdd
    },
    /**
     * 设置当前操作, 注意这里只记录一些需要的关键操作
     */
    setCurOperationBySpecial(type: string) {
      this.curOperationBySpecial = <any>type;
    },

    /**
     * 设置当前的操作状态, 目前只有 'CELL' | 'LINE' | 'DEVICE' | 'AREA'
     * @param type
     */
    setCurSelectType(type: "CELL" | "LINE" | "DEVICE" | "AREA") {
      //如果相同的情况下，重置当前选中状态为CELL
      if(type === this.curSelectType){
        this.curSelectType = 'CELL'
      }else{
        this.curSelectType = <any>type;
      }
    },

    /**
     * 设置一个预置变量
     * @param data {...}
     */
    setTemporaryData(data: { [K: string]: any }) {
      this.temporaryData = data;
    },

    /**
     * 清空预置变量
     */
    clearTemporaryData() {
      this.temporaryData = null;
    },

    /**
     * 进入批量新增点位模式
     */
    setAddBatchCellPattern() {
      this.isAddBatchCellPattern = true;
    },

    /**
     * 取消批量新增点位模式
     */
    clearAddBatchCellPattern() {
      this.isAddBatchCellPattern = false;
      this.addBatchByLineData = null;
    },
    /**
     * 选点模式
     */

    setSingleChoiceSelectMode(selectCell: string|undefined) {
      this.isSingleSelectMode = true;
      this.isMultipleSelectMode = false;

      const { selectNodes, selectIndex, layerName } = this;

      this.multipleSelTemporaryData = {
        selectNodes,
        selectIndex,
        layerName,
      };

      editRef.value?.resetAllSelected();

      selectCell && editRef.value?.setSelected({
        layerName: <string>layerName,
        id: selectCell,
      });
    },
    /**
     * 批量选点模式
     */
    setMultipleSelectMode(selectCells: string[]) {
      this.isSingleSelectMode = false;
      this.isMultipleSelectMode = true;
      const { selectNodes, selectIndex, layerName } = this;

      this.multipleSelTemporaryData = {
        selectNodes,
        selectIndex,
        layerName,
      };

      editRef.value?.resetAllSelected();

      selectCells.length && editRef.value?.multiSearch([
        {
          layerName: "CELL",
          ids: selectCells,
        },
      ]);
      editRef.value?.setMultiSelectedStatus(true);
    },
    /**
     * 取消批量选点模式
     */
    clearMultipleSelectMode(): any[] {
      const curSelect = editRef.value?.getAllSelected() || [];
      editRef.value?.resetAllSelected();
      const { selectNodes, selectIndex, layerName } = <any>this.multipleSelTemporaryData;
      if (selectNodes.length === 1) {
        editRef.value?.setMultiSelectedStatus(false);
        editRef.value?.setSelected({
          layerName: layerName,
          id: selectNodes.map((item: any) => item.id)[0],
        });
      } else if (selectNodes.length > 1) {
        editRef.value?.multiSearch([
          {
            layerName: "CELL",
            ids: selectNodes.map((item: any) => item.id),
          },
        ]);
      }
      this.selectNodes = selectNodes;
      this.selectIndex = selectIndex;
      this.layerName = layerName;
      this.multipleSelTemporaryData = null;
      this.isMultipleSelectMode = false;
      this.isSingleSelectMode = false;

      return curSelect;
    },
    /**
     * 进入功能选择模式
     */
    setFnChooseMode() {
      console.log('执行set')
      this.isFnChooseMode = true;
    },
    clearFnChooseMode() {
      console.log('执行reset')
      this.isFnChooseMode = false
    },

    setAddBatchByLineData(data: MapSegmentDto) {
      this.addBatchByLineData = data;
    },

    clearAddBatchByLineData() {
      this.addBatchByLineData = null;
    },

    setGlobalLoading(isLoading: boolean) {
      this.globalLoading = isLoading;
    }
  },
});
