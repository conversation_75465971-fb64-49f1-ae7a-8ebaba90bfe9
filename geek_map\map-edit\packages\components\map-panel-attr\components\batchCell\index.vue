<template>
  <div class="attrPanel">
    <!-- title -->
    <p class="title">{{ $t("lang.rms.fed.batchAddPoints") }}</p>
    <el-scrollbar>
      <FromBase
        class="formBase"
        labelWidth="100px"
        ref="baseTabsRef"
        :form-data.async="baseData"
        :formItem="baseItem"
        @update:form-data="change"
      >
      </FromBase>
    </el-scrollbar>
    <el-row class="btns" :gutter="20">
      <el-col class="btn" :offset="4" :span="9">
        <el-button type="primary" @click="finished">{{ $t("lang.rms.fed.startDrawing") }}</el-button>
      </el-col>
      <el-col class="btn" :span="6">
        <el-button @click="exit">{{ $t("lang.rms.fed.exit") }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ref, watch } from "vue";
import { FromBase } from "../base/index";
import { useAttrStore } from "@packages/store/attr";
import { ADD_BATCH_BASE_CONF_FN } from "@packages/configure/fromData/addBatchCellConf";
import { useEditMap } from "@packages/hook/useEdit";
import { OMNI_DIR_CELL } from "@packages/configure/dict/nodeType";
import { defOperationFn } from "@packages/hook/useEvent";
import { ElMessage } from "element-plus";
const editMap = useEditMap();
const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());
const baseTabsRef = ref();
const isInit = ref(true);
const baseData = ref({
  width: 1,
  length: 1,
  cellNum: 2,
  spacing: 1,
  cellType: OMNI_DIR_CELL,
  isCellLine: true,
  points: [],
});
const baseItem = computed(() => {
  return ADD_BATCH_BASE_CONF_FN(attrStore).formItem;
});
//绘图完成后重置数
const initBaseData = info => {
  if (!info || !isInit.value) return;
  const { points } = info;
  const [p1, p2] = points;
  const { x: x1, y: y1 } = p1;
  const { x: x2, y: y2 } = p2;
  const sliceX = x2 - x1;
  const sliceY = y2 - y1;
  const dis = Math.sqrt(sliceX * sliceX + sliceY * sliceY);
  baseData.value.spacing = dis;
  baseData.value.cellNum = 2;
  isInit.value = false;
};
watch(
  attrStoreRef.addBatchByLineData,
  value => {
    const fromData = baseTabsRef.value?.getFormData() || baseData.value;
    baseData.value = { ...fromData, points: value?.points || [] };
    initBaseData(value);
  },
  {
    deep: true,
    immediate: true,
  },
);

function change(option: any) {
  const { addBatchByLineData } = attrStoreRef;
  if (option.points && addBatchByLineData.value) {
    addBatchByLineData.value!.points = option.points || [];
    const data = [{ ...addBatchByLineData.value, points: option.points }];
    editMap.value?.updateElements({
      id: "LINE",
      data,
      isSaveHistory: false,
    });
  }
}

function getRef() {
  return baseTabsRef.value;
}

// 创建单元格
function createCell(createData: any) {
  if (!attrStore.addBatchByLineData) {
    ElMessage.info("没有绘制线段, 创建失败！");
    exit();
    return;
  }
  const addOps: { id: string; data: any[] } = { id: "CELL", data: [] };
  const paths = attrStore.addBatchByLineData?.points;
  console.log(createData);
  const { cellNum, cellType, spacing } = createData;
  if (paths) {
    const { x: x1, y: y1 } = paths[0];
    const { x: x2, y: y2 } = paths[1];
    const at2 = Math.atan2(y2 - y1, x2 - x1);
    // const sliceX = (x2 - x1) / (cellNum - 1);
    // const sliceY = (y2 - y1) / (cellNum - 1);
    const sliceX = spacing * Math.cos(at2);
    const sliceY = spacing * Math.sin(at2);
    const width = baseData.value.width;
    const length = baseData.value.length;
    for (let i = 0; i < cellNum; i++) {
      const nodeId = editMap.value?.createId();
      const location = {
        x: Number((x1 + sliceX * i).toFixed(3)),
        y: Number((y1 + sliceY * i).toFixed(3)),
      };
      const startBounds = {
        x: Number((location.x - length / 2).toFixed(3)),
        y: Number((location.y - width / 2).toFixed(3)),
      };
      const ops = {
        location,
        nodeId,
        id: nodeId,
        mapEditItemId: nodeId,
        isQrNode: false,
        nonstandardNode: false,
        startBounds,
        width,
        length,
        cellType,
        nonstandardNode: false,
      };
      addOps.data.push(ops);
    }
  }

  editMap.value?.addElements(addOps);
  editMap.value?.resetMode();
  return addOps.data;
}

//是否创建线段
function createLine(createData: any, dataList: any[]) {
  if (createData?.isCellLine) {
    const lineDataList = [];

    const points = dataList.map(ops => {
      const {
        location: { x, y },
        nodeId,
      } = ops;
      return { x, y, nodeId, cellCode: null };
    });

    const pointLne = points.length;

    for (let index = 1; index < pointLne; index += 1) {
      const pointItem = [points[index - 1], points[index]];
      lineDataList.push({
        segmentId: editMap.value?.createId(),
        points: pointItem,
        segmentType: "S_LINE",
        loadDirs: 2,
        unloadDirs: 2,
      });
    }

    const addOps = {
      id: "LINE",
      data: lineDataList,
    };

    editMap.value?.addElements(addOps);
  }
}

//完成
function finished() {
  const createData = baseTabsRef.value.getFormData();
  //创建元素
  const dataList = createCell(createData);
  dataList && createLine(createData, dataList);
  exit();
}

// 退出
function exit() {
  //删除参考线段
  const cellData = attrStore.addBatchByLineData;
  const segmentId = cellData?.segmentId;
  segmentId &&
    editMap.value?.deleteElements({
      id: "LINE",
      data: [segmentId],
      isSaveHistory: false,
    });

  // 移除批量模式
  attrStore.clearAddBatchCellPattern();

  // 恢复到无操作模式
  defOperationFn(<any>editMap.value);
  // 取消选中任何元素
  attrStore.clearSelect();
}

defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  width: 300px;

  .title {
    font-size: 16px;
    margin: 0px 0 20px;
    font-weight: 900;
  }

  .formBase {
    padding-right: 15px;
  }

  .btns {
    margin: 0;
    height: 70px;
    line-height: 50px;

    .btn {
      text-align: center;
    }
  }
}
</style>
