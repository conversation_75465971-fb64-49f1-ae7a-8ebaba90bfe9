<template>
  <div class="attrPanel" :class="{ isBatchModifyCellFun }">
    <TabFromBase
      v-if="curSelectNode"
      ref="baseTabsRef"
      :fromData="curSelectNode"
      :title="$t(title)"
      :tabs="tabData"
      @change="change"
    >
      <template #pinpointSlot>
        <Pinpoint />
      </template>

      <template #functionsSlot="{ option }">
        <Functions :fromData="option.fromData" :updateValue="option.update" />
      </template>

      <template #cellTraySetSolt="{ option }">
        <TraySetVue :fromData="option.fromData" :updateValue="option.update" />
      </template>
    </TabFromBase>

    <el-row :gutter="20" class="btns" v-if="isBatchModifyCellFun">
      <el-col class="btnBox" :offset="6" :span="6">
        <el-button class="btn" @click="cancelBatchModify">{{ $t("lang.rms.fed.cancel") }}</el-button>
      </el-col>
      <el-col class="btnBox" :span="6">
        <el-button class="btn" type="primary" @click="sureBatchModify">{{ $t("lang.rms.fed.confirm") }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch, nextTick } from "vue";
import TabFromBase from "../base/tabBase.vue";
import { useAttrStore } from "@packages/store/attr";
import Pinpoint from "./components/pinpoint.vue";
import Functions from "./components/functionItem.vue";
import TraySetVue from "./components/traySet.vue";
import { getTabConf, getTabItemConfByName, changeBaseTabData, batchChangeBaseTabData } from "../common";
import { NODE_CELL, SHELF_CELL, STATION_CELL } from "@packages/configure/dict/nodeType";
// import { NODE_CELL } from "@packages/configure/dict/nodeType";
import { getComponentStore } from "@packages/store/piniaSync";
import { defOperationFn } from "@packages/hook/useEvent";
import { useEditMap } from "@packages/hook/useEdit";

const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(attrStore);
const editMap = useEditMap();

  // 添加校验状态
  const validationErrors = ref<{[key: string]: string}>({});

const props = defineProps<{
  panelConf?: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
  title: string;
}>();

// const hiddenTabs: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenTabs || []);
// 在lite 1.1 中改变了tab 的显示逻辑只有节点类似是货架点，工作站时展示支架模型，其他类型不展示
const hiddenTabs: ComputedRef<string[]> = computed(() => {
  const cellType = attrStoreRef.curNodeDataByIndex.value?.cellType;
  // 只有货架点/工作站显示支架模型tab
  if (cellType !== SHELF_CELL && cellType !== STATION_CELL) {
    // "model" 是支架模型tab的name，确认无误
    return [...(props.panelConf?.hiddenTabs || []), "model"];
  }
  return props.panelConf?.hiddenTabs || [];
});
const hiddenCodes: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenCodes || []);
const disabledCodes: ComputedRef<string[]> = computed(() => props.panelConf?.disabledCodes || []);
const baseTabsRef = ref();

// 这里是批量编辑功能的逻辑
const menuStore = getComponentStore("menuStore")();
const menuStoreRef = menuStore ? storeToRefs(menuStore) : null;
const isBatchModifyCellFun: ComputedRef<boolean> = computed(() => {
  if (!menuStoreRef) {
    return false;
  }
  return <boolean>menuStoreRef.isBatchModifyCellFun.value;
});

const tabData = computed(() => {
  // 如果是批量编辑功能
  if (isBatchModifyCellFun.value) {
    return [getTabItemConfByName(NODE_CELL, "func", baseTabsRef.value)];
  }

  return getTabConf(
    {
      hiddenTabs: hiddenTabs.value,
      hiddenCodes: hiddenCodes.value,
      disabledCodes: disabledCodes.value,
      type: NODE_CELL,
    },
    attrStore,
    baseTabsRef.value,
  );
});

const curSelectNode = computed(() => {
  if (isBatchModifyCellFun.value) {
    return {};
  }
  return attrStoreRef.curNodeDataByIndex.value;
});

watch(
  attrStoreRef.curNodeDataByIndex,
  value => {
    if (isBatchModifyCellFun.value) {
      return;
    }
    baseTabsRef.value && baseTabsRef.value.setItemAll(value);
  },
  {
    deep: true,
    immediate: true,
  },
);

async function change(option: any) {
  // 只在新增时赋值 在lite1.1版本的时候 隐藏了叉车角度的UI添加 默认给后端传空  (这里先注释掉如果测试没问题在删除这段代码)
  // const isAdd = !option.nodeId;
  // if (isAdd) {
  //   // 
  //   option.needAngles = [];
  //   if (option.palletRackDto) {
  //     option.palletRackDto.angle = null;
  //   } else {
  //     option.palletRackDto = { angle: null };
  //   }
  // }
  if (isBatchModifyCellFun.value) {
    // TODO: 当批量编辑功能点的时候, 不再实时去更新节点数据, 只有在点击完成时一次更新
    // batchChangeBaseTabData(option, attrStore);
  } else {
     try {
        const formRef = baseTabsRef.value;
        if (formRef && formRef.getValidateFormModel) {
          // 进行表单校验
          console.log("表单校验--------------------------", await formRef.getValidateFormModel())
          await formRef.getValidateFormModel();
          // 校验通过，清除错误状态
          validationErrors.value = {};
          // 更新数据
          changeBaseTabData(option, attrStore);
        } else {
          // 如果没有校验方法，直接更新（向后兼容）
          changeBaseTabData(option, attrStore);
        }
      } catch (error) {
        // 校验失败，不更新数据
        console.warn('表单校验失败，不更新节点数据:', error);
        // 可以在这里添加用户提示
        // ElMessage.warning('输入内容不符合要求，请检查后重试');
      }
    
  }
}

function getRef() {
  return baseTabsRef.value;
}

function cancelBatchModify() {
  defOperationFn(<any>editMap.value, {
    isDefMode: true,
    isClearSelect: true,
  });
}

function sureBatchModify() {
  const option = baseTabsRef.value.getFormData();
  batchChangeBaseTabData(option, attrStore);
  nextTick(() => {
    cancelBatchModify();
  });
}

defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;

  &.isBatchModifyCellFun {
    padding-bottom: 40px;
    box-sizing: border-box;
  }

  .btns {
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}
</style>
