// 居中相关
import { ToolPanelType } from "@packages/type/editUiType";

// 居中
export const VERTICALLY_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-chuizhijuzhong-L",
    name: "verticallyCenter",
    group: "center",
    isSelect: false,
    describe: "水平居中",
    eventName: "map:verticallyCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};
// 居中
export const HORIZONTAL_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-shuipingjuzhong-L",
    name: "horizontalCenter",
    group: "center",
    isSelect: false,
    describe: "垂直居中",
    eventName: "map:horizontalCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};
// 上居中
export const UP_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-shangduiqi",
    name: "upCenter",
    group: "center",
    isSelect: false,
    describe: "顶对齐",
    eventName: "map:upCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};

// 下居中
export const DOWN_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-xiaduiqi",
    name: "downCenter",
    group: "center",
    isSelect: false,
    describe: "底对齐",
    eventName: "map:downCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};

// 左居中
export const LEFT_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-zuoduiqi-L",
    name: "leftCenter",
    group: "center",
    isSelect: false,
    describe: "左对齐",
    eventName: "map:leftCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};

// 右居中
export const RIGHT_CENTER: ToolPanelType = {
  option: {
    icon: "map-font-youduiqi-L",
    name: "rightCenter",
    group: "center",
    isSelect: false,
    describe: "右对齐",
    eventName: "map:rightCenter",
    //只能通过Esc清空激活态
    resetByEsc: true,
  },
};
