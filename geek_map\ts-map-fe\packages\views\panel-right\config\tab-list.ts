import { getPermissionTab } from "../../../singleton";
const tabs = getPermissionTab();
const tabList = [
  {
    key: "OrderGroupRobot",
    layer: "robot",
    name: "机器人",
    label: "lang.rms.fed.robot",
    sortable: false,
  },
  {
    key: "OrderGroupShelf",
    layer: "shelf",
    name: "货架",
    label: "lang.rms.fed.shelf",
  },
  {
    key: "OrderGroupCell",
    layer: "cell",
    name: "单元格",
    label: "lang.rms.fed.cell",
  },
  {
    key: "OrderGroupCharger",
    layer: "charger",
    name: "充电站",
    label: "lang.rms.fed.tabChargingStationManagement",
  },
  // 在lite1.1中暂时隐藏这三个
  // {
  //   key: "OrderGroupDevice",
  //   layer: "device",
  //   name: "设备",
  //   label: "lang.rms.fed.deviceManage",
  // },
  // {
  //   key: "OrderGroupInstruction",
  //   name: "业务指令",
  //   label: "lang.rms.fed.tabBusinessInstructions",
  // },
  // {
  //   key: "OrderGroupZone",
  //   name: "控制分区管理",
  //   label: "lang.rms.fed.tabControlZoneManagement",
  // },
  {
    key: "OrderGroupWarehouse",
    name: "仓库管理",
    label: "lang.rms.fed.tabWarehouseManagement",
  },
  {
    key: "OrderGroupDeadlock",
    layer: "robot",
    name: "解死锁",
    label: "lang.rms.fed.deadlockResolution",
  },
  {
    key: "OrderGroupPalletRack",
    layer: "palletRack", //或者可能是 pallet 还未创建
    name: "托盘管理",
    label: "auth.rms.operator.pallet",
  },
  {
    key: "OrderGroupTaskLimit",
    // layer: "robot",
    name: "解除任务限制",
    label: "lang.rms.fed.congestionCtl",
  },
];
let nowList: Array<any> = [];
const isGuestRole = _$utils.getRMSPermission() === false && _$utils.getRoleInfo() === "guest";
tabList.forEach(item => {
  if (isGuestRole && item.key === "OrderGroupWarehouse") return;
  if (tabs.includes(item.key)) nowList.push(item);
});
// nowList.push({
//   key: "OrderGroupDeadlock",
//   layer: "robot",
//   name: "解死锁",
//   label: "lang.rms.fed.deadlockResolution",
// });

//临时显示
// nowList.push({
//   key: "OrderGroupPalletRack",
//   layer: "palletRack",
//   name: "托盘管理",
//   label: "auth.rms.operator.pallet",
// });

export default nowList;
