export default {
  namespaced: true,
  state: {
    tabModelActive: "",
    layoutOpen: 0, // 布局弹窗开关
    currentLang: localStorage.getItem("curLanguage") || "zh_cn",// 获取当前使用的语言
    addHJModalData: {}, // 新增加的模型
    groundData: {},
    hJModalWarn: "", // 警示语
    emptySwich: 0, // 清空  每次+1  执行一次清除
    activeIDData: "", // 当前选中id数据
    editData: {
      // 编辑模型数据
      modelCategory: "SHELF",
      modelName: "",
      categoryId: "",
      length: "",
      width: "500",
      height: "500",
      passLength: 0,
      passWidth: 0,
      passHeight: 0,
      move: "1",
      legLength: 0,
      legWidth: 0,
    }, // tab要修改的数据
    // 货架类型的编码
    shelfModelTypeValue:"",
    // 地面支架模型的编码
    groundModelTypeValue:"",
    // 托盘模型的编码
    palletModelTypeValue:"",
    // 托盘架模型的编码
    pralletModelTypeValue :"",

    maxModelId: 0, // 下发模型
    shelfCategoryDict: [], // 容器类别

    modelCategoryDict: [],

    // 异形货架
    specialShapedShelfModelView: "mainView", // 异形货架当前展示页面
    specialShapedShelfModelViewType: "", // 异形货架(新增/编辑/查看)
    specialShapedShelfModelViewData: null, // 异形货架当前展示页面相关数据

    // 托盘模型
    trayModelView: "mainView", // 货架模型页面
    trayModelViewType: "", // 货架模型(新增/编辑/查看)
    trayModelViewData: null, // 货架模型当前展示页面相关数据

    // 托盘支架模型
    trayRackModelView: "mainView", // 货架模型页面
    trayRackModelViewType: "", // 货架模型(新增/编辑/查看)
    trayRackModelViewData: null, // 货架模型当前展示页面相关数据
    sizeTypeDict: [], // sizetype字典
  },
  getters: {
    getEmptySwich(state) {
      return state.emptySwich;
    },
    latestCode (state) {
      return state.latestCode;
    }
  },
  actions: {
    // 获取类型编码
    async fetchAutoGenModelType({ commit }, type) {
      const res = await $req.get("/athena/shelfModel/getAutoGenModelType", {
        type 
      });
      if(type==="S"){
        commit("setShelfModelType", res.data);
      }else if(type==="SH"){
        commit("setGroundModelType", res.data);
      }else if(type==="P"){
        commit("setPalletModelType", res.data);
      }else{
        commit("setPrModelType", res.data);
      }
    },

    async fetchMaxModelId({ commit }) {
      const res = await $req.get("/athena/shelfModel/getMaxId");
      commit("setMaxModelId", res.data || 0);
    },
    async fetchShelfCategory({ commit }) {
      const res = await $req.post("/athena/shelfCategory/findAll");
      commit(
        "setShelfCategory",
        res.data.map(i => ({ label: i.categoryName, value: i.id, type: i.categoryType })) || 0,
      );
    },
    async findDistinctSizeType({ commit }) {
      const res = await $req.get("/athena/robot/manage/findDistinctSizeType");
      commit("setSizeType", res.data || 0);
    },
  },
  mutations: {
    setTabModelActive(state, data) {
      state.tabModelActive = String(data);
    },
    setLayoutOpen(state) {
      console.log("打开");
      state.layoutOpen++;
    },
    setEditData(state, data) {
      state.editData = Object.assign({}, data);
    },
    setEmptySwich(state) {
      state.emptySwich++;
    },
    setHJModalData(state, shelfData) {
      shelfData.passLength = shelfData.length - (2*shelfData.legLength)
      shelfData.passWidth = shelfData.width - (2*shelfData.legWidth)
      shelfData.updateUser = localStorage.getItem("userName") || ""
      state.addHJModalData = Object.assign({}, shelfData);

      if (!shelfData.modelType) {
        state.hJModalWarn = "lang.venus.web.check.pleaseInputCode";
        return;
      }
      if (!shelfData.modelName) {
        state.hJModalWarn = "lang.rms.fed.pleaseEnterModelName";
        return;
      }

      state.hJModalWarn = "";
    },
    setGroundData(state, groundData) {
      state.groundData = Object.assign({}, groundData);
    },
    setActiveIdData(state, data) {
      state.activeIDData = data;
    },
    setMaxModelId(state, modelId) {
      state.maxModelId = modelId;
    },
    setShelfCategory(state, shelfCategory) {
      state.shelfCategoryDict = shelfCategory;
    },
    resetModelView(state) {
      state.specialShapedShelfModelView = "mainView";
      state.trayRackModelView = "mainView";
      state.trayModelView = "mainView";
    },
    // ==
    setSpecialShapedShelfModelView(state, viewName) {
      state.specialShapedShelfModelView = viewName;
    },
    setSpecialShapedShelfModelViewData(state, data) {
      state.specialShapedShelfModelViewData = data;
    },
    setSpecialShapedShelfModelViewType(state, type) {
      state.specialShapedShelfModelViewType = type;
    },
    // == 托盘模型
    setTrayModelView(state, viewName) {
      state.trayModelView = viewName;
    },
    setTrayModelViewData(state, data) {
      state.trayModelViewData = data;
    },
    setTrayModelViewType(state, type) {
      state.trayModelViewType = type;
    },
    // 托盘支架模型
    setTrayRackModelView(state, viewName) {
      state.trayRackModelView = viewName;
    },
    setTrayRackModelViewData(state, data) {
      state.trayRackModelViewData = data;
    },
    setTrayRackModelViewType(state, type) {
      state.trayRackModelViewType = type;
    },
    setSizeType(state, sizeTypeList) {
      state.sizeTypeDict = sizeTypeList;
    },
    // 切换当前语言
    setCurrentLang(state, lang) {
      state.currentLang = lang;
    },
    setShelfModelType(state,data){
      state.shelfModelTypeValue = data
    },
    // 地面支架的模型编码
    setGroundModelType(state,data){
      state.groundModelTypeValue = data
    },
    // 托盘模型的模型编码
    setPalletModelType(state,data){
      state.palletModelTypeValue = data
    },
    // 托盘架模型
    setPrModelType(state,data){
      state.pralletModelTypeValue = data

    }
  },
};
