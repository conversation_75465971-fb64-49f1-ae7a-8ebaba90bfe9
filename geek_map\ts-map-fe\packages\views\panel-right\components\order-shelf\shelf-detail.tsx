/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  shelf: shelfData;
};
function ShelfDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [data, setData] = useState(null);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.shelf) {
      setData(null);
      return;
    }
    setData(props.shelf);
  }, [props.shelf]);

  return (
    data && (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.shelfCoding"),
            value: data?.shelfCode || "--",
          },
          {
            label: t("lang.rms.fed.type"),
            value: data?.shelfType || "--",
          },
          // lite1.1版本这里对地图监控点击货架的时候
          // 
          // {
          //   label: t("lang.rms.fed.length"),
          //   value: data?.length || "--",
          // },
          // {
          //   label: t("lang.rms.fed.width"),
          //   value: data?.width || "--",
          // },
          {
            label: t("lang.rms.fed.state"),
            value: data?.shelfStatus || "--",
          },
          {
            label: t("lang.rms.fed.textNodeCode"),
            value: data?.locationCell || "--",
          },
          {
            label: t("lang.rms.fed.textAbsoluteCoordinate"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.fed.textIndexCoordinates"),
            value: data?.locationIndex || "--",
          },
          {
            label: t("lang.rms.fed.angle"),
            value: data.hasOwnProperty("radAngle") ? data.radAngle : "--",
          },
          {
            label: t("lang.rms.fed.robotId"),
            value: data.robotId !== -1 ? data.robotId : "--",
          },
          {
            label: t("lang.rms.fed.placementCode"),
            value: data?.placementCell || "--",
          },
          {
            label: t("lang.rms.fed.shelfHeatDisplay"),
            value: String(data?.score) || "--",
          },
        ]}
      />
    )
  );
}

export default ShelfDetail;
