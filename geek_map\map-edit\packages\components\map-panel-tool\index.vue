<template>
  <div class="tool-panel" :class="`${config.align} ${config.border ? 'border' : ''}`">
    <div class="tool-divider">
      <template v-for="(item, index) in innerValue" :key="item.name">
        <el-popover
          v-if="getIsPopover(item)"
          ref="popoverRef"
          :virtual-ref="toolPanelItemRefs[index]"
          trigger="hover"
          :placement="placement"
          :effect="config.effect"
          virtual-triggering
        >
          <span v-if="item.option?.describe">{{ item.option?.describe || "" }}</span>
          <toolpanel
            refs="childRefs"
            :model-value="item"
            @trigger="transparentEmits"
            @update:modelValue="updataChildValue"
          ></toolpanel>
        </el-popover>
        <!-- 这是是地图里的所有图标 -->
        <ToolPanelItem
          ref="toolPanelItemRefs"
          v-if="item.option"
          :model-value="item.option"
          :placement="placement"
          :isPopoverChild="config.align === 'left' && item.children?.length"
          @click="handelItemClick(item)"
        />
      </template>
    </div>

    <!-- 如果不是悬浮 -->
    <template
      v-if="
        curActiveConfData &&
        curActiveConfData.children?.length &&
        (curActiveConfData.config?.model || 'expand') === 'expand'
      "
    >
      <toolpanel
        refs="childRefs"
        v-model="curActiveConfData"
        @trigger="transparentEmits"
        @update:modelValue="updataChildValue"
      ></toolpanel>
    </template>
  </div>
</template>
<script lang="ts">
export default {
  name: "toolpanel",
};
</script>
<script setup lang="ts">
/**
 * 如果 align 是top 则定高,  是left则定宽
 */
import ToolPanelItem from "./panelItem.vue";
import { defineProps, nextTick, computed, ComputedRef, ref, Ref, watch } from "vue";
import { getPanelToolDisabled } from "./common";
interface ToolPanelItem {
  icon?: string; // 图标
  name: string; // TODO: 传入的 name 一定不要重复⭐⭐⭐
  title?: string;
  className?: string;
  describe?: string; // 详情
  eventName?: string; // 事件名称, 点击时
  active?: boolean; // 是否选中, 注意, 如果这里的isSelect为false, 且当前没有children数据, 则该参数不生效
  isSelect?: boolean; // 是否是可切换选中状态
  group?: string; // 组, isSelect 为true时生效, 一组中只能有一个选中状态
  disabled?: Function;
  defDisabled?: boolean;
  data?: { [key: string]: any };
  [key: string]: any;
}

interface ToolPanelConf {
  align?: "top" | "left"; // 组件的展示位置, 仅支持 TOP/LEFT
  effect?: "dark" | "light"; // 弹出层样式
  className?: string; // 附加的className
  defActive?: boolean; // 是否默认选中
  model?: "popper" | "expand"; // 模式, 悬浮或拓展, 注意, 第一层只能是拓展
  border?: boolean; // 当前按钮组是否有border
}

interface Conf {
  option?: ToolPanelItem; // 第一层不需要加, 后面必须加
  config?: ToolPanelConf;
  children?: Conf[];
}

interface PropsType {
  modelValue: Conf;
}

/**
 * 传入的属性
 */
const props = defineProps<PropsType>();

// 属性
const config: ComputedRef<ToolPanelConf> = computed(() => {
  return {
    align: "top",
    effect: "dark",
    model: "expand",
    ...props.modelValue.config,
  };
});

const children: ComputedRef<Conf[] | undefined> = computed(() => {
  return props.modelValue.children;
});

/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "update:modelValue", data: Conf): void;
  (event: "trigger", data: any): void;
}>();

// 内置value, 以保证当使用该组件时没有用upate:modelValue时也能正常展示
const innerValue: Ref<Conf[]> = ref([...(children.value || [])]);
const toolPanelItemRefs = ref();

const curActive: ComputedRef<string> = computed(() => {
  return (
    innerValue.value.find(item => {
      const { option } = item;
      return option?.active && !option.isSelect && item.children;
    })?.option?.name || ""
  );
});
// 默认选中
config.value.defActive && setDefActive();

watch(
  () => props.modelValue,
  value => (innerValue.value = [...(value.children || [])]),
);

// 检查默认选中
watch(innerValue, value => {
  config.value.defActive && setDefActive();
});

// 根据 align 属性推断 popover 弹出层应该处于哪种状态
const placement: ComputedRef<string> = computed(() => {
  const align = config.value.align;
  return { top: "bottom", left: "right" }[align as string] || "bottom";
});

function getIsPopover(item: Conf) {
  if (item.children?.length && item.config?.model === "popper" && toolPanelItemRefs.value) {
    return true;
  }
  return false;
}

const curActiveConfData: ComputedRef<Conf | undefined> = computed(() => {
  return (children.value || []).find(item => item.option?.name === curActive.value);
});

/**
 * 点击事件
 */
const handelItemClick = (item: Conf) => {
  console.log(item);
  if (item.option?.name) {
    setActive(item.option.name);

    if (item.children?.length) {
      item.children.forEach(item => {
        item.option!.active = false;
      });
    }

    item.option?.eventName &&
      nextTick(() => {
        emits("trigger", item);
      });
  }
};

function transparentEmits(item: Conf) {
  emits("trigger", item);
}

/**
 * 设置默认值
 * 当当前设备没有选中状态时, 才能生效
 */
function setDefActive() {
  const { value } = innerValue;
  if (value.length && !value.find(item => item.option?.active && !item.option?.isSelect)) {
    /**
     * 能够预设默认值的按钮, 需要满足以下条件
     * 1. 不能是禁用状态
     * 2. 需要存在子按钮组
     * 3. 不能是已经avtive的状态
     * 4. 不能是 isSelect 状态(可开关)
     */
    const findItem = innerValue.value.find(({ option, children }) => {
      return getPanelToolDisabled(option) && children?.length && !option?.active && !option?.isSelect;
    });

    if (findItem && findItem.option?.name) {
      setActive(findItem.option?.name);
    }
  }
}

/**
 * 设置默认值
 * 如果当前按钮存在 isSelect 状态(可开关), 则不会影响平级的其他按钮
 * 如果按钮有子按钮组, 则取消其他按钮组的展示状态
 */
function setActive(selectName: string, active?: boolean, children?: Conf[]): void {
  const list = children || [...innerValue.value];
  list.forEach(item => {
    const { name, isSelect, group } = item.option || {};
    // 操作的是否是当前item
    const isSelectItem = selectName === name;
    if (name && isSelectItem) {
      if (isSelect) {
        // 如果按钮存在 isSelect

        // curSetActive: 需要变更的active
        const curSetActive = active === undefined ? !item.option?.active : active;

        // 如果存在组效果, 则清空其他同名组的active效果
        if (group && curSetActive) {
          (list || []).forEach(child => {
            const isName = child.option?.name === name;
            const isGroup = child.option?.group === group;
            isGroup && (child.option!.active = isName);
          });
        } else {
          item.option!.active = curSetActive;
        }
      } else if (!item.children?.length) {
        // 如果没有 isSelect, 且没有 children, 如果指定了 active 则赋值, 如果没有指定则无响应
        active !== undefined && (item.option!.active = active);
      } else if (item.children?.length) {
        // 如果有 children 则设置当前选中状态, 如果当前是选中, 还需要取消其他按钮的选中状态, isSelect除外
        const curSetActive = active === undefined ? !item.option?.active : active;
        (list || []).forEach(child => {
          const isName = child.option?.name === name;
          const isSelect = child.option?.isSelect;
          if (isName) {
            child.option!.active = curSetActive;
          } else if (curSetActive && !isSelect) {
            child.option!.active = false;
          }
        });
      }
    }
  });

  if (!children) {
    innerValue.value = list;
    updateValue();
  }
}

function setDisabled(selectName: string, disabled?: boolean, children?: Conf[]): void {
  const list = children || [...innerValue.value];
  list.forEach(item => {
    const { name } = item.option || {};
    // 操作的是否是当前item
    const isSelectItem = selectName === name;
    if (name && isSelectItem) {
      item.option!.defDisabled = disabled;
    }
  });

  if (!children) {
    innerValue.value = list;
    updateValue();
  }
}

function getOptionByChild(name: string, children: Conf[]): Conf | undefined {
  const childLen = children.length;

  // 先查当前按钮组的数据, 再查子集数据
  for (let index = 0; index < childLen; index += 1) {
    const item = children[index];
    if (item.option?.name === name) {
      return item;
    } else if (item.children?.length) {
      // 查子集
      const option = getOptionByChild(name, item.children);
      if (option) return option;
    }
  }

  // 都没有 返回undefined
}

function getParentByChild(name: string, children: Conf[], parent: Conf): Conf | undefined {
  const childLen = children.length;

  // 先查当前按钮组的数据, 再查子集数据
  for (let index = 0; index < childLen; index += 1) {
    const item = children[index];
    if (item.option?.name === name) {
      return parent;
    } else if (item.children?.length) {
      // 查子集
      const option = getParentByChild(name, item.children, item);
      if (option) return option;
    }
  }

  // 都没有 返回undefined
}

// 数据更新后向上传递更新内容
function updateValue() {
  const children = innerValue.value;
  emits("update:modelValue", {
    config: config.value,
    option: props.modelValue.option,
    children: children,
  });
}
/**
 * 子组件向上传递的同步数据
 * 1. 子组件的内容发生了变更, 向上传递数据
 * 2. 当前容器接收到子组件内容后, 更新对应节点数据(根据name匹配)
 * 3. 当前组件继续向上传递更新
 */
function updataChildValue(newValue: Conf) {
  const name = newValue.option?.name;
  if (name) {
    const children = innerValue.value;
    const childrenLen = children.length;
    for (let index = 0; index < childrenLen; index += 1) {
      const childItem = children[index];
      // 匹配到之后直接替换, 并继续向上传递更新
      if (childItem.option?.name === name) {
        children[index] = childItem;
        emits("update:modelValue", {
          config: config.value,
          option: props.modelValue.option,
          children: children,
        });
        break;
      }
    }
  }
}

// 设置某个name的active选中态
function setActiveByName(name: string, active: boolean = true) {
  const item = getParentByName(name);
  item && setActive(name, active, item.children);
  updateValue();
}

/**
 * 设置某个name的disabled状态
 * @param name
 * @param disabled
 */
function setDisabledByName(name: string, disabled: boolean = false) {
  const item = getParentByName(name);
  item && setDisabled(name, disabled, item.children);
  updateValue();
}

// 获取某个name的acitve选中态
function getActiveByName(name: string): boolean {
  const item = getOptionByName(name);
  return item?.option?.active || false;
}

// 清除某个name下的数据选中态, 前提是该name下有数据
function clearChildActiveByName(name: string) {
  const item = getOptionByName(name);
  if (item) {
    item.children?.forEach(childItem => {
      // console.log(childItem)
      // if(!childItem.option!.resetByEsc){
      //   childItem.option!.active = false;
      // }
      childItem.option!.active = false;
    });
    updateValue();
  }
}

function clearChildActiveByNameExclude(name: string) {
  const item = getOptionByName(name);
  if (item) {
    item.children?.forEach(childItem => {
      if (!childItem.option!.resetByEsc) {
        childItem.option!.active = false;
      }
    });
    updateValue();
  }
}
// 获取某个name的父级节点
function getParentByName(name: string) {
  return getParentByChild(name, innerValue.value, props.modelValue);
}

// 获取某个name对应的数据内容
function getOptionByName(name: string): Conf | undefined {
  return getOptionByChild(name, innerValue.value);
}

/**
 * 获取某层级下的选中内容
 * @param path
 */
function getChildActiveItemByPath(path: string[]) {
  const len = path.length;
  if (path) {
    let curData: Conf | undefined = {
      children: innerValue.value,
    };

    for (let index = 0; index < len; index += 1) {
      if (curData?.children) {
        curData = curData.children.find(item => {
          return item.option?.name === path[index];
        });
      } else {
        // 走到这里说明已经找不到这一层数据了
        return null;
      }
    }

    // 到这里, curData 就是最终要找的层了
    if (curData?.children) {
      return curData?.children.find(item => item.option?.active);
    }

    return null;
  }
}

/**
 * 获取第二层级的选中状态, 比较常用
 */
function getSecondChildActiveOptByName(name: string) {
  return getChildActiveItemByPath([name])?.option;
}

defineExpose({
  setActiveByName,
  setDisabledByName,
  getActiveByName,
  clearChildActiveByName,
  clearChildActiveByNameExclude,
  getParentByName,
  getOptionByName,
  updateValue,
  getSecondChildActiveOptByName,
  getChildActiveItemByPath,
});
</script>

<style lang="scss">
.tool-panel {
  background: #fff;
  &.top {
    &.border {
      box-shadow: 1px 0px 3px 1px #eee;
    }

    .tool-panel-item {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        height: 20px;
        top: 10px;
        width: 1px;
        background: #eee;
      }

      &:first-child {
        &::before {
          display: none;
        }
      }
    }
  }

  &.left {
    width: 36px;
    height: 100%;
    border-right: 1px solid #eee;
    display: inline-block;
    &.border {
      box-shadow: 1px 1px 3px 1px #eee;
    }

    .tool-panel-item {
      margin-top: 1px;
    }
  }

  .tool-divider {
    border-bottom: 1px solid #eee;
    font-size: 0;
  }
}
</style>
