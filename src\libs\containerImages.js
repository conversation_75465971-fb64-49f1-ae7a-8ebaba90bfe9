// 这个是容器模型根据当前使用的语言来判断 目前只支持中文和英文
function getContainerImage(name,lang = "zh_cn") {
  
    // require 动态路径只能部分变量化，因此拆成对象映射
    const zhImages = {
      "shelf.svg": require("@/imgs/container-backups/shelf.svg"),
      "double-pallet.svg":require('@imgs/container-backups/double-pallet.svg'),
      "single-pallet.svg":require("@imgs/container-backups/single-pallet.svg"),
      "bracket.svg":require("@imgs/container-backups/bracket.svg")
    };
  
    const enImages = {
      "shelf.svg": require("@/imgs/container/shelf.svg"),
      "double-pallet.svg" :require('@imgs/container/double-pallet.svg'),
      "single-pallet.svg":require("@/imgs/container/single-pallet.svg"),
      "bracket.svg":require("@imgs/container/bracket.svg")
    };
  
    const map = lang === "zh_cn" ? zhImages : enImages;
    return map[name] || ""; // 找不到返
  }
  
  export default getContainerImage;
