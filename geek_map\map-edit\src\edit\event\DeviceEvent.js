import LayerManager from "../layerManager/LayerManager";
import Mode from "../Mode";
import Selected from "../selected/Selected";
import {pixi2cad,toFixed,isHoverNode,createId} from '../utils/utils'
import Control from "../control/Control";
import Event from '../event/Event'
import {defaultConfig} from '../config'
import DICT from "@packages/configure/dict";
const {CELL} = defaultConfig
export default class DeviceEvent {
  static addEvents() {
    //新添加的设备单元格，为了能与设备产生关联关系，需要生成mapEditItemId，mapEditItemId与nodeId相同
    const nodeId = createId()
    //生成设备下的单元格
    const createDeviceCell = (location,deviceType) => {
      const cellType = `${deviceType}_CELL`
      //默认宽高
      const width = CELL.width,length = CELL.length;
      //计算startBounds
      const startBounds = {x:toFixed(location.x - length / 2),y:toFixed(location.y - width / 2)}
      // const nodeId = Math.round(Math.random() * 100000)
      const addOp = {
        id:'CELL',
        data: [
          {
            location,
            nodeId,
            id:nodeId,
            cellType,
            isQrNode:false,
            startBounds,
            width,
            length,
            loadDirs:null,
            unloadDirs:null,
            //与nodeID相同，适配原先接口
            mapEditItemId:nodeId
          }
        ],
        isEmitData:false
      }
      LayerManager.addElements(addOp)
    }
    const events = {
      clicked:e => {
        const mode = Mode.mode
        const {action,options} = mode
        const {name} = options
        Selected.resetAllSelected()
        //默认宽高
        const {width,length} = defaultConfig[name]
        const addProperties = {}
        const id = createId()
        const hoverNode = isHoverNode(e.world)
        //赋默认值
        Object.assign(addProperties,{id,deviceType:name,...defaultConfig[name]})
        // 如果是充电桩，给他的交互模式 设置的默认值为：交互模式 这个是lite1.1 版本的逻辑
        if(name === 'CHARGER') {
          addProperties.interactiveMode = DICT.INTERACTIVE_MODE_DICT_ON // 设置默认值
        }
        if(hoverNode) {
          const {x, y, nodeId, cellCode} = hoverNode
          const location = pixi2cad({x:toFixed(x),y:toFixed(y)})
          const mapEditItemId = cellCode ? null : nodeId
          Object.assign(addProperties,{location,cellCode,mapEditItemId})
        }else{
          const {x,y} = e.world
          const location = pixi2cad({x:toFixed(x),y:toFixed(y)})
          Object.assign(addProperties,{location,cellCode:null,mapEditItemId:nodeId})
          //marker反光柱设备的特殊处理，可以不添加在单元格上
          // if(name !== 'MARKER'){
          if(!['MARKER','SAFE'].includes(name)){
            createDeviceCell(location,name)
          }
        }
        //添加marker特殊处理，添加code和startBounds字段
        if(name === 'MARKER'){
          const markerInstance = LayerManager.get('MARKER')
          const code = markerInstance.id2$el.size + 1
          const {location} = addProperties
          //计算startBounds
          const startBounds = {x:toFixed(location.x - length / 2),y:toFixed(location.y - width / 2)}
          Object.assign(addProperties,{code,startBounds})
        }
        //添加设备
        const addOp = {
          id: name,
          data: [addProperties]
        }
        Mode.resetMode()
        LayerManager.addElements(addOp)
      },
    }
    return events
  }
  static onDragStart($el,e) {
    if(!$el.selected) return;
    Event.isClick = true
    Event.timestamp = Date.now()
    Control.enableDrag(false)
    if(!$el.selected) return;
    if(Event.activeKey !== 'Alt') return $el.dragging = false;
    $el.data = e.data;
    $el.dragging = true;
  }
  static onDragEnd($el,e) {
    Event.isClick = (Date.now() - Event.timestamp) < 300
    $el.dragging = false;
    $el.data = null;
    Control.enableDrag(true)
  }
  static onDragMove($el,e) {
    const {data} = $el
    const newPosition = data.getLocalPosition($el.parent);
    $el.x = newPosition.x;
    $el.y = newPosition.y;
  }
}
