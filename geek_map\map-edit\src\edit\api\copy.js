import Store from "../store/Store";
import { defaultConfig } from "../config";
import LayerManager from "../layerManager/LayerManager";
import { cad2pixi, isHoverNode, toFixed, createId } from "../utils/utils";
import Selected from "../selected/Selected";
//不允许复制的功能
const disabled = ['AVOID_CELL', 'BACKUP_CELL', 'QUEUE_CELL', 'RECYCLEID', 'WAIT_FOR_TASK_CELL', 'WAIT_CELL', 'VIA_CELL', 'DOCKING_CELL'];
import { cloneDeep } from 'lodash';
const copy = function (offsetX = 0, offsetY = 0, copyNum = 1) {
  for (let i = 0; i < copyNum; i++) {
    const curoffsetX = offsetX + offsetX * (i);
    const curoffsetY = offsetY + offsetY * (i);

    copy_only.call(this, curoffsetX, curoffsetY, false)
  }
  Selected.resetAllSelected()
}
const copy_only = function (offsetX = 0, offsetY = 0, isResetAllSelected = true) {
  const selectedData = this.getAllSelected()
  if (!selectedData.length) return
  //跟复制单元格有关的线段
  let relatedSegmentId = []
  //复制跟单元格相关的
  let relatedDeviceId = []
  //保存框选的所有单元格nodeId
  const copyCellNodeId = [];

  const copyCellData = selectedData.map(selected => {
    const { properties } = selected
    const { location: { x, y }, cellCode, nodeId: oldNodeId, cellType, sizeType, isQrNode, width, length, loadDirs, unloadDirs, startBounds: { x: bx, y: by }, functions } = { ...properties }
    //复制方法
    let copyFunctions = null;
    if (functions) {
      copyFunctions = cloneDeep(functions)
      copyFunctions = copyFunctions.filter(fn => {
        return !disabled.includes(fn.funcType)
      })
    }
    const segmentIdArr = Store.node2Line.getLine(oldNodeId)
    copyCellNodeId.push(oldNodeId);
    const relatedId = cellCode || oldNodeId
    const deviceIdArr = Store.cellCode2Device.getDevice(relatedId)
    if (deviceIdArr) {
      relatedDeviceId.push(...deviceIdArr)
    }
    if (segmentIdArr) {
      relatedSegmentId.push(...segmentIdArr)
    }
    const offsetLocation = { x: toFixed(x + offsetX), y: toFixed(y + offsetY) }
    const offsetStartBounds = { x: toFixed(bx + offsetX), y: toFixed(by + offsetY) }
    const newNodeId = createId()
    //获取单元格默认配置
    const defaultCellConf = defaultConfig[cellType] || defaultConfig['CELL']
    const addOps = {
      ...defaultCellConf,
      nodeId: newNodeId,
      location: offsetLocation,
      startBounds: offsetStartBounds,
      mapEditItemId: newNodeId,
      functions: copyFunctions,
      sizeType,
      width,
      length,
      isQrNode,
      cellType,
      loadDirs,
      unloadDirs,
      // nonstandardNode:false
    }
    return addOps
  })

  //先生成单元格
  LayerManager.addElements({
    id: 'CELL',
    data: copyCellData,
    isEmitData: false,
    // historyContinue:false
  })
  //线id去重
  relatedSegmentId = [...new Set(relatedSegmentId)]
  if (relatedSegmentId.length) {
    const copyLineData = []
    relatedSegmentId.forEach(oldSegmentId => {
      const { properties } = LayerManager.getProperties({ layerName: 'LINE', id: oldSegmentId })
      const { points, unloadDirs, loadDirs, segmentType } = properties
      const nodeIdArr = points.map(p => p.nodeId).filter(nodeId => nodeId);
      const isCopy = nodeIdArr.every(nodeId => copyCellNodeId.includes(nodeId))
      //判断这条线的两个端点是否都存在于单元格上
      if (isCopy) {
        const newSegmentId = createId()
        const newPoints = points.map((p, i) => {
          const { x, y } = p
          const nx = x + offsetX, ny = y + offsetY
          //cad
          const nPos = { x: toFixed(nx), y: toFixed(ny) }
          //转化为pixi坐标
          const nPixiPos = cad2pixi(nPos)
          const isHover = isHoverNode(nPixiPos, true)
          if (isHover) {
            const { nodeId, cellCode } = isHover
            //如果是曲线只有第一个和第四个点进行吸附,直线则全部吸附
            const isAdsorb = segmentType === 'BEZIER' && [0, 3].includes(i) || segmentType === 'S_LINE'
            return { ...nPos, nodeId: isAdsorb ? nodeId : null, cellCode: isAdsorb ? cellCode : null }
          } else {
            return { ...nPos, nodeId: null, cellCode: null }
          }
        })
        const lineProperties = { points: newPoints, unloadDirs, loadDirs, segmentType, segmentId: newSegmentId }
        copyLineData.push(lineProperties);
      }
      // return {points:newPoints,unloadDirs,loadDirs,segmentType,segmentId:newSegmentId}
    })
    //先生成线段
    LayerManager.addElements({
      id: 'LINE',
      data: copyLineData,
      isEmitData: false,
      historyContinue: true
    })
  }
  if (relatedDeviceId.length) {
    //需要重置的属性
    const resetOps = { cellCode: null, dbId: null, hostCode: null }
    relatedDeviceId.forEach(item => {
      const { id, layerName } = item
      const { properties } = LayerManager.getProperties({ layerName, id })
      const { width, length, location } = properties
      const { x, y } = location
      const nx = x + offsetX, ny = y + offsetY
      //cad
      const nPos = { x: toFixed(nx), y: toFixed(ny) }
      //转化为pixi坐标
      const nPixiPos = cad2pixi(nPos)
      const isHover = isHoverNode(nPixiPos, true)
      const newDeviceId = createId()
      const deviceDefaultConfig = defaultConfig[layerName] || {}
      const copyOps = { id: newDeviceId, location: nPos, deviceType: layerName, ...deviceDefaultConfig, ...resetOps }

      //marker的特殊处理
      if (layerName === 'MARKER') {
        const markerInstance = LayerManager.get('MARKER')
        const code = markerInstance.id2$el.size + 1
        const startBounds = { x: toFixed(nPos.x - length / 2), y: toFixed(nPos.y - width / 2) }
        Object.assign(copyOps, { code, startBounds, width, length })
      }
      if (isHover) {
        const { nodeId, cellCode } = isHover
        const mapEditItemId = cellCode ? null : nodeId
        Object.assign(copyOps, { cellCode, mapEditItemId })
      } else {
        Object.assign(copyOps, { cellCode: null, mapEditItemId: null })
      }
      LayerManager.addElements({
        id: layerName,
        data: [copyOps],
        isEmitData: false,
        historyContinue: true
      })
    })
  }

  if (isResetAllSelected) {
    Selected.resetAllSelected()
  }
}
export { copy }
