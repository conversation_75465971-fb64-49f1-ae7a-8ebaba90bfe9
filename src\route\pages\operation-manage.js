/* ! <AUTHOR> at 2022/09/21 */

/** 运营管理 */
export default [
  /** ************** 异常处理 ****************/
  {
    path: "/operationManage/exceptionHand",
    name: "exceptionHand",
    meta: {
      title: "auth.rms.exceptionHandling.page",
      auth: "auth.rms.exceptionHandling.page",
      pid: "/operationManage",
    },
    component: () => import("@views/operationManage/exceptionHand"),
  },
  /** ************** 维修调度 ****************/
  // {
  //   path: "/operationManage/robotRecycle",
  //   name: "robotRecycle",
  //   meta: {
  //     title: "auth.rms.maintenanceSchedule.page",
  //     auth: "auth.rms.maintenanceSchedule.page",
  //     pid: "/operationManage",
  //     noPermissionGuest: true,
  //   },
  //   component: () => import("@views/operationManage/robotService"),
  // },
  /** ************** 回调管理 ****************/
  {
    path: "/operationManage/callbackManage",
    name: "callbackManage",
    meta: {
      title: "auth.rms.page.menu.callbackManagement",
      auth: "auth.rms.page.menu.callbackManagement",
      pid: "/operationManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/operationManage/callbackManage"),
  },
  /** ************** 操作日志管理 ****************/
  {
    path: "/operationManage/operateLog",
    name: "operateLog",
    meta: {
      title: "auth.rms.warehouseManage.operateLog",
      auth: "auth.rms.warehouseManage.operateLog",
      pid: "/operationManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/operationManage/operateLog"),
  },
  /** ************** 机器人软件管理 ****************/
  // {
  //   path: "/operationManage/softwareControl",
  //   name: "softwareControl",
  //   meta: {
  //     title: "auth.rms.robotSoftwareManager.page",
  //     auth: "auth.rms.robotSoftwareManager.page",
  //     pid: "/operationManage",
  //   },
  //   component: () => import("@views/operationManage/softwareManagement"),
  // },
  /** ************** 机器人升级日志 ****************/
  // {
  //   path: "/operationManage/versionControl",
  //   name: "versionControl",
  //   meta: {
  //     title: "auth.rms.robotUpgradeLog.page",
  //     auth: "auth.rms.robotUpgradeLog.page",
  //     pid: "/operationManage",
  //   },
  //   component: () => import("@views/operationManage/robotUpgradeLog"),
  // },
  /** ************** 充电站软件管理 ****************/
  // {
  //   path: "/operationManage/chargeSoftwareManager",
  //   name: "chargeSoftwareManager",
  //   meta: {
  //     title: "auth.rms.chargeSoftwareManager.page",
  //     auth: "auth.rms.chargeSoftwareManager.page",
  //     pid: "/operationManage",
  //   },
  //   component: () => import("@views/operationManage/rysMonitorRobotControl"),
  // },
  /** ************** 数据清理 ****************/
  {
    path: "/operationManage/dbClean",
    name: "dbClean",
    meta: {
      title: "auth.rms.hygeia.cleanData",
      auth: "auth.rms.hygeia.cleanData",
      pid: "/operationManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/operationManage/dbClean"),
  },
];
