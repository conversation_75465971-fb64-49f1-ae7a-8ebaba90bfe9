/* ! <AUTHOR> at 2022/08/29 */
import BoxTool from "./components/box-tool";
import MsgInfo from "./components/msg-info";
import FloorListBox from "./components/change-floor";
import FilterSelect from "./components/filter-select";
import MapLeftTools from "./components/map-left-tools";
import MapRightTools from "./components/map-right-tools";
// import RackAbnormal from "./components/rack-abnormal";

type PropsType = {
  messageCount: { backLogCount: number; notificationCount: number };
  robotException: boolean;
};
function TopPanel(props: PropsType) {
  return (
    <div className="map2d-top-panel">
      <div style={{ display: "flex", alignItems: "center" }}>
        <MapLeftTools />
        <MsgInfo messageCount={props.messageCount} robotException={props.robotException} />
      </div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <FilterSelect />
        {/* <RackAbnormal /> */}
        <MapRightTools />
        <FloorListBox />
        <BoxTool />
      </div>
    </div>
  );
}

export default TopPanel;
