/**
 * 这里集中处理保存/初始化数据
 * ⭐⭐⭐⭐⭐⭐⭐⭐⭐⭐
 */
import { MapNodeDto, MapSegmentDto, MapAreaDto } from "@packages/type/editNode";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";
import { useEditMap, reloadMapData } from "./useEdit";
import mapVNode from "@packages/hook/useMapVNode";
import { validateMap, saveMap } from "@packages/api/map";
import { ValidateMapParams, MapFloorDto, MoveOperationDtos } from "@packages/api/map/type/validateMap";
import { ElMessage } from "element-plus";
import { nextTick } from "vue";
/* 这个函数主要是将后退功能是否后退在保存之前给后端传 “是” 因为新增功能功能里的后退功能里
   是否强制后退的表单项的默认值是后端的反过来的默认值
   暂时前端强制转化了，如果跟后端协调一下他那么对全局没有什么影响可以把这个强制转化给删了
*/
function forceBackupCellFuncA(items: any[] | undefined): void {
  if (!items || !Array.isArray(items)) {
    return;
  }
  items.forEach((item: any) => {
    // 检查 item 是否包含 functions 数组
    if (item?.functions && Array.isArray(item.functions)) {
      // 在 functions 数组中查找 funcType 为 'BACKUP_CELL' 的功能对象
      const backupCellFunc = item.functions.find((func: any) => func.funcType === "BACKUP_CELL");
      if (backupCellFunc) {
        // 如果找到了 BACKUP_CELL 功能，强制将 funcA 设置为 '1'
        backupCellFunc.funcA = "1";
      }
    }
  });
}
function getSaveParams() {
  const editMap = useEditMap();
  const appStore = useAppStore();
  const mapId: string | number = appStore.mapId || "";
  const floorId: string | number = appStore.floorId || "";
  const mapData = editMap.value?.getAllData();
  const deleteData = editMap.value?.getDeleteData();
  const mapAreaList: any[] = mapData.AREA || [];
  const mapChargerDtoList: any[] = mapData.CHARGER || [];
  const mapDeviceItemDtoList: any[] = [];
  const mapFloorMaskItemDtoList: any[] = [];
  const mapMarkerList: any[] = mapData.MARKER;
  const mapSegmentDtoList: any[] = mapData.LINE || [];
  const mapStationDtoList: any[] = mapData.STATION || [];
  //安全设备
  const dmpDeviceDtoList: any[] = mapData.SAFE || [];
  /**
   * ⭐ 电梯数据在提交的时候会将其中的 queueCellsCodeIdMap 字段移除,
   * 提交此字段后端会报错, 且后端会自动生成该字段
   */
  const mapElevatorDtoList: any[] = (mapData.ELEVATOR || []).map(({ queueCellsCodeIdMap, ...item }: any) => ({
    ...item,
  }));

  /**
   * ⭐ node数据必须带 renderID 而且不能重复...
   */
  // 这里设置着，在添加的时候给needAngles 字段设置为[]
  const mapNodeDtoList: any[] =
    mapData.CELL.map((item: any, index: number) => {
      // 如果是新增节点（没有 dbId），则设置 needAngles 为空数组
      // 如果是编辑节点（有 dbId），则保持原有的 needAngles 值
      const baseItem = { ...item, renderID: index };
      // 只在新增时添加 needAngles 字段这个是在lite1.1 中
      if (!item.dbId) {
        return { ...baseItem, needAngles: [] };
      }

      return baseItem;
    }) || [];

  const mapFloorDto: MapFloorDto = {
    mapId: Number(mapId),
    floorId: Number(floorId),
    mapAreaList,
    mapChargerDtoList,
    mapDeviceItemDtoList,
    mapFloorMaskItemDtoList,
    mapMarkerList,
    mapNodeDtoList,
    mapSegmentDtoList,
    mapStationDtoList,
    mapElevatorDtoList,
    dmpDeviceDtoList,
    deleteData,
  };
  /**
   * moveOperationDtos 全局点位编辑数据
   */
  const moveOperationDtos: MoveOperationDtos[] = useAttrStore().moveOperationDtos || [];

  const validateMapParams: ValidateMapParams = {
    mapId,
    mapFloorDto,
    moveOperationDtos,
  };

  return validateMapParams;
}

/**
 * 保存数据
 * @returns
 */
export const saveEditMap = async () => {
  const validateMapParams = getSaveParams();
  const attrStore = useAttrStore();
  attrStore.setGlobalLoading(true);
  try {
    forceBackupCellFuncA(validateMapParams?.mapFloorDto?.mapNodeDtoList);
    forceBackupCellFuncA(validateMapParams?.mapFloorDto?.mapAreaList);
    const { code, data } = await validateMap(<ValidateMapParams>validateMapParams);
    if (code === 0 && Object.keys(data).length === 0) {
      attrStore.setGlobalLoading(false);
      const saveData = await saveMap(validateMapParams);
      if (saveData && saveData.code === 0) {
        attrStore.setStoredCellCodes(validateMapParams.mapFloorDto.mapNodeDtoList);
        await nextTick();
        ElMessage.success("保存成功！");
        const leftPanelRef = mapVNode.useLeftPanels();
        leftPanelRef.setDisabledByName("save", true);
        reloadMapData();
      }
    } else {
      const mapValidateDialog = mapVNode.useMapValidateDialog();
      attrStore.setGlobalLoading(false);
      mapValidateDialog.tootip(data);
    }
  } catch (error) {
    attrStore.setGlobalLoading(false);
  }
};

export async function getMapValidateMap(moveOperationDtos: MoveOperationDtos[]) {
  const validateMapParams = getSaveParams();
  return await validateMap({ ...validateMapParams, moveOperationDtos });
}

export function getMapDataByLocation(option: MapNodeDto) {
  // const { location, startBounds, indexX, indexY, width, length } = option;
  // return {
  //   width,
  //   length,
  //   indexX,
  //   indexY,
  //   locationX: location.x,
  //   locationY: location.y,
  //   startBoundsX: startBounds.x,
  //   startBoundsY: startBounds.y,
  // };
}
