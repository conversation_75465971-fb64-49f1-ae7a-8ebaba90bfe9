// 线段 - 控制

import { NodeAttrEditConf } from "@packages/type/editUiType";
import { useI18n } from "@packages/hook/useI18n";
import { LINE_CONTROL_SUPPORTSIDEWAY_DICT } from "@packages/configure/dict/supportSideWayDict";
import { AVOID_OBSTACLES, AVOID_OBSTACLES_NOLOAD, AVOID_OBSTACLES_NOLOAD_LOAD, AVOID_OBSTACLES_LOAD } from "@packages/configure/dict/AVOID_OBSTACLES";
export const LINE_CONTROL_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "line-control",
    tabTitle: "lang.rms.fed.control",
    formItem: [
      {
        label: "lang.rms.fed.speedLimit",
        describe: `${t("lang.rms.fed.robotSegmentMaxSpeedLimit")}m/s`,
        isTitle: true,
      },
      {
        prop: "unloadMaxSpeed",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
        appendAttrsFn(value: string, allData: any) {
          return {
            disabled: [AVOID_OBSTACLES_NOLOAD, AVOID_OBSTACLES_NOLOAD_LOAD].includes(allData.obstacleAvoidance),
          };
        },
      },
      {
        prop: "loadMaxSpeed",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
        appendAttrsFn(value: string, allData: any) {
          return {
            disabled: [AVOID_OBSTACLES_LOAD, AVOID_OBSTACLES_NOLOAD_LOAD].includes(allData.obstacleAvoidance),
          };
        },
      },
      {
        label: "lang.rms.fed.obsAvoWperToVehicleHead",
        describe: `${t("lang.rms.fed.robotSegmentObstacleWidth")}/mm`,
        isTitle: true,
      },
      {
        prop: "unloadObstacleRangeAround",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        prop: "loadObstacleRangeAround",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        label: "lang.rms.fed.obsAvoHheadingDirection",
        describe: `${t("lang.rms.fed.robotSegmentObstacleWidth")}/mm`,
        isTitle: true,
      },
      {
        prop: "unloadObstacleRange",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        prop: "loadObstacleRange",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      // 是否横移 lite 1.1 隐藏了这两个的值
      // {
      //   prop: "supportSideWay",
      //   label: "lang.rms.fed.enableLateralMovement",
      //   describe: `lang.rms.fed.whetherEnableTraverseMsg`,
      //   component: "elSelect",
      //   data: LINE_CONTROL_SUPPORTSIDEWAY_DICT || []
      // },
      // 安全避障
      {
        prop: "obstacleAvoidance",
        label: "lang.rms.edit.map.safeObstacleAvoidance",
        describe: `lang.rms.fed.isCloseAvoidanceTip`,
        component: "elSelect",
        data: AVOID_OBSTACLES || []
      },
      // 高空避障 lite1.1 隐藏了这两个的值
      // {
      //   prop: "highAreaObstacleAvoidance",
      //   label: "lang.rms.fed.highAltObsAvoidance",
      //   describe: `lang.rms.fed.isCloseAvoidanceTip1`,
      //   component: "elSelect",
      //   data: AVOID_OBSTACLES || []
      // },
    ],
  };
};
