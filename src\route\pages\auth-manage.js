/* ! <AUTHOR> at 2021/02 */

/** 权限管理 */
export default [
  /** ************** 用户列表 ****************/
  {
    path: "/authManage/userList",
    name: "userList",
    meta: {
      title: "lang.rms.fed.tabUserList",
      auth: "user",
      pid: "/authManage",
      mustPermission: true,
    },
    component: () => import("@views/authManage/iframeUserList"),
  },

  /** ************** 角色列表 ****************/
  {
    path: "/authManage/roleList",
    name: "roleList",
    meta: {
      title: "lang.rms.fed.tabRoleList",
      auth: "userAuth",
      pid: "/authManage",
      mustPermission: true,
    },
    component: () => import("@views/authManage/iframeRoleList"),
  },
  /** ************** license管理 ****************/
  // {
  //   path: "/authManage/licenseManage",
  //   name: "licenseManage",
  //   meta: {
  //     title: "auth.rms.fed.licenseManage",
  //     auth: "auth.rms.fed.licenseManage",
  //     pid: "/authManage",
  //     mustPermission: true,
  //   },
  //   component: () => import("@views/authManage/licenseManage"),
  // },
];
