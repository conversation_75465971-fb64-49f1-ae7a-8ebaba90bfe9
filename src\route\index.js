/* ! <AUTHOR> at 2021/01 */

import Vue from "vue";
import VueRouter from "vue-router";
import routerPre from "./each.route"; // 全局导航守卫

Vue.use(VueRouter);
import MonitorRouter from "./pages/monitor"; // 地图
import WarehouseManageRouter from "./pages/warehouse-manage";
import AuthManageRouter from "./pages/auth-manage";
import OperationManageRouter from "./pages/operation-manage";
import SystemConfigRouter from "./pages/system-config";
import RedirectRouter from "./pages/redirect-path"; // 需要重定向的router  给别的系统做引用兼容

const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "login",
    meta: { title: "login", auth: false, notMenu: true, noPermissionGuest: true },
    component: () => import("@views/login"),
  },

  {
    path: "",
    name: "layout",
    redirect: "/dashboard",
    component: () => import("@views/layout"),
    children: [
      {
        path: "/dashboard",
        name: "dashboard",
        meta: {
          title: "lang.rms.fed.home",
          icon: "dashboard",
          auth: false,
          noPermissionGuest: true,
        },
        component: () => import("@views/dashboard"),
      },
      ...MonitorRouter,
      {
        path: "/warehouseManage",
        meta: {
          title: "lang.rms.page.menu.warehouse",
          icon: "warehouse",
          auth: "auth.rms.warehouseManage.page",
          noPermissionGuest: true,
        },
      },
      // lite1.1隐藏整个运营管理
      // 运营管理
      // {
      //   path: "/operationManage",
      //   meta: {
      //     title: "lang.rms.page.menu.opsManagement",
      //     icon: "example",
      //     auth: "auth.rms.warehouseMonitor.page",
      //     noPermissionGuest: true,
      //   },
      // },
      {
        path: "/systemConfig",
        meta: {
          title: "lang.rms.page.menu.systemConfig",
          icon: "component",
          auth: "auth.rms.page.menu.systemConfig",
          noPermissionGuest: true,
        },
      },
      {
        path: "/statistics",
        name: "statistics",
        meta: {
          title: "数据统计",
          notMenu: true,
          auth: false,
          noPermissionGuest: true,
        },
        component: () => import("@views/statistics"),
      },
      {
        path: "/authManage",
        redirect: "/authManage/userList",
        meta: {
          title: "lang.rms.fed.tabPrivilegeManagement",
          icon: "peoples",
          auth: "auth.rms.account",
          mustPermission: true,
        },
      },
      ...WarehouseManageRouter,
      ...OperationManageRouter,
      ...AuthManageRouter,
      ...SystemConfigRouter,
      {
        path: "/404",
        name: "404",
        meta: { title: "404", auth: false, notMenu: true, noPermissionGuest: true },
        component: () => import("@views/errorPage/404"),
      },
      ...RedirectRouter,
    ],
  },
];

const router = new VueRouter({ routes });
routerPre(router);

export default router;
