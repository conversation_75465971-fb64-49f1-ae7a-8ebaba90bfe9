#!/bin/sh
echo ">>>>>>>>>>>>>>> start jenkins_build.sh"
pwd 

rms_path="rms-fe"
monitor2d_path="geek_map/ts-map-fe"
edit2d_path="geek_map/map-edit"
# 地图监控2D
echo ">>>>>>>>>>>>>>> 开始 ${monitor2d_path} install"

cd ${WORKSPACE}/${rms_path}/${monitor2d_path}
pwd
npm install
echo ">>>>>>>>>>>>>>> 完成 ${monitor2d_path} install"

# 地图监控3D
echo "*************** 地图监控3D不需要 npm install"

# 地图编辑
echo ">>>>>>>>>>>>>>> 开始 ${edit2d_path} install"
cd ${WORKSPACE}/${rms_path}/${edit2d_path}
pwd
npm install
echo ">>>>>>>>>>>>>>> 完成 ${edit2d_path} install"

echo ">>>>>>>>>>>>>>> build 切换目录到主目录"
cd ${WORKSPACE}/${rms_path}
pwd

# 进入项目文件夹
echo ">>>>>>>>>>>>>>> 开始 ${rms_path} npm intall"
npm install
echo ">>>>>>>>>>>>>>> 完成 ${rms_path} intall"

# 确认代码安全扫描
# echo ">>>>>>>>>>>>>>> 开始 确认代码安全扫描"
# npm run check:scan
# echo ">>>>>>>>>>>>>>> 完成 确认代码安全扫描"

# build
npm run jenkins:build
echo ">>>>>>>>>>>>>>> build 打包结束"

# 判断打包
if [ -d "./dist" ]
then 
  echo ">>>>>>>>>>>>>>> 打包成功"
  exit 0
else
  echo "*************** 打包失败!!!"
  exit 1
fi

