@font-face {
  font-family: "monitorMap"; /* Project id 3630595 */
  src: url('iconfont.woff2?t=1734681203075') format('woff2'),
       url('iconfont.woff?t=1734681203075') format('woff'),
       url('iconfont.ttf?t=1734681203075') format('truetype');
}

.monitorMap {
  font-family: "monitorMap" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.monitor-fontsanjiaojiantoushang:before {
  content: "\e66b";
}

.monitor-font-road-hot:before {
  content: "\e618";
}

.monitor-font-show-charge-ID:before {
  content: "\e601";
}

.monitor-font-region-management:before {
  content: "\e749";
}

.monitor-font-bumadian:before {
  content: "\e651";
}

.monitor-font-show-help-doc:before {
  content: "\e7ab";
}

.monitor-font-cell-hot:before {
  content: "\ebda";
}

.monitor-font-to-3d:before {
  content: "\e67e";
}

.monitor-font-voice-tips:before {
  content: "\e60f";
}

.monitor-font-show-load-road-direct:before {
  content: "\e624";
}

.monitor-font-full-screen:before {
  content: "\e625";
}

.monitor-font-shelf-heat-display:before {
  content: "\e626";
}

.monitor-font-display-all-path:before {
  content: "\e627";
}

.monitor-font-show-station-ID:before {
  content: "\e628";
}

.monitor-font-show-unload-road-direct:before {
  content: "\e62a";
}

.monitor-font-zoom-out:before {
  content: "\e62b";
}

.monitor-font-zoom-in:before {
  content: "\e62c";
}

.monitor-font-map-reset:before {
  content: "\e63c";
}

.monitor-font-external-obstacle:before {
  content: "\e63e";
}

.monitor-font-show-preempted-area:before {
  content: "\e615";
}

